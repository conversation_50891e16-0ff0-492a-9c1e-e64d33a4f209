package com.link.riderlink.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.util.Log
import com.link.riderlink.utils.ThemeManager.themeMode
import java.util.LinkedList


object ThemeManager {
    //是否跟随配置
    private var followConfig = true

    // 默认是日间模式
    private var currentThemeMode = ThemeMode.DAY

    // 主题模式监听器
    private val themeChangeListeners: MutableList<OnThemeChangeListener> = LinkedList()

    // 夜间资源的缓存，key : 资源类型, 值<key:资源名称, value:int值>
    private val cachedNightResources = HashMap<String, HashMap<String, Int>>()

    // 夜间模式资源的后缀，比如日件模式资源名为：R.color.activity_bg, 那么夜间模式就为 ：R.color.activity_bg_night
    private const val RESOURCE_SUFFIX = "_night"

    /**
     * 根据传入的日间模式的resId得到相应主题的resId，注意：必须是日间模式的resId
     * * *
     * @param dayModeResourceId 日间模式的resId
     * @return 相应主题的resId，若为日间模式，则得到dayResId；反之夜间模式得到nightResId
     */
    fun getCurrentThemeRes(context: Context, dayModeResourceId: Int): Int {
        if (themeMode == ThemeMode.DAY) {
            return dayModeResourceId
        }
        // 资源名
        val entryName = context.resources.getResourceEntryName(dayModeResourceId)
        // 资源类型
        val typeName = context.resources.getResourceTypeName(dayModeResourceId)
        var cachedNightResourcesByType = cachedNightResources[typeName]
        // 先从缓存中去取，如果有直接返回该id
        if (cachedNightResourcesByType == null) {
            cachedNightResourcesByType = HashMap()
        }
        val resId = cachedNightResourcesByType[entryName + RESOURCE_SUFFIX]
        Log.e("ThemeManager", "getCurrentThemeRes: ${context.packageName}")
        Log.e(
            "ThemeManager",
            "getCurrentThemeRes dayModeResourceId:$dayModeResourceId typeName:$typeName entryName:${entryName + RESOURCE_SUFFIX}"
        )
        if (resId != null && resId != 0) {
            return resId
        } else {
            //如果缓存中没有再根据资源id去动态获取
            try {
                // 通过资源名，资源类型，包名得到资源int值
                val nightResId = context.resources.getIdentifier(
                    entryName + RESOURCE_SUFFIX,
                    typeName,
                    context.packageName
                )
                Log.e("ThemeManager", "getCurrentThemeRes nightResId:$nightResId")
                // 放入缓存中
                cachedNightResourcesByType[entryName + RESOURCE_SUFFIX] = nightResId
                cachedNightResources[typeName] = cachedNightResourcesByType

                return nightResId
            } catch (e: Resources.NotFoundException) {
                e.printStackTrace()
            }
        }
        return 0
    }

    /**
     * 注册ThemeChangeListener
     *
     * @param listener
     */
    fun registerThemeChangeListener(listener: OnThemeChangeListener) {
        if (!themeChangeListeners.contains(listener)) {
            themeChangeListeners.add(listener)
        }
    }

    /**
     * 反注册ThemeChangeListener
     *
     * @param listener
     */
    fun unregisterThemeChangeListener(listener: OnThemeChangeListener) {
        if (themeChangeListeners.contains(listener)) {
            themeChangeListeners.remove(listener)
        }
    }

    /**
     * 根据传入值切换元素ID
     *
     * @param dayModeResourceId
     *
     * @param nightModeResourceId
     */
    fun autoChangeInt(dayModeResourceId: Int, nightModeResourceId: Int): Int {
        if (themeMode == ThemeMode.DAY) {
            Log.e("autoChangeInt", "autoChangeInt: $dayModeResourceId")
            return dayModeResourceId
        } else {
            Log.e("autoChangeInt", "autoChangeInt: $nightModeResourceId")
            return nightModeResourceId
        }
    }

    /**
     * 根据传入值切换元素ID
     *
     * @param dayModeString
     *
     * @param nightModeString
     */
    fun autoChangeStr(dayModeString: String, nightModeString: String): String {
        return if (themeMode == ThemeMode.DAY) {
            dayModeString
        } else {
            nightModeString
        }
    }

    fun showBar(show: Boolean, barColor: Int) {
        if (themeChangeListeners.isNotEmpty()) {
            for (listener in themeChangeListeners) {
                listener.onThemeBarChanged(show, barColor)
            }
        }
    }

    var themeMode: ThemeMode
        /**
         * 得到主题模式
         *
         * @return
         */
        get() = currentThemeMode
        /**
         * 设置主题模式
         *
         * @param themeMode
         */
        set(themeMode) {
            Log.e("MainActivity", "onset: $themeMode")
            if (currentThemeMode != themeMode) {
                currentThemeMode = themeMode
                if (themeChangeListeners.size > 0) {
                    for (listener in themeChangeListeners) {
                        listener.onThemeChanged()
                    }
                }
            }
        }

    /**
     * 主题模式，分为日间模式和夜间模式
     */
    enum class ThemeMode {
        DAY, NIGHT
    }

    /**
     * 主题模式切换监听器
     */
    abstract class OnThemeChangeListener {
        /**
         * 主题切换时回调
         */
        open fun onThemeChanged() {}

        /**
         * 主题导航状态栏改变
         */
        open fun onThemeBarChanged(show: Boolean, barColor: Int) {}
    }

    /**
     * 判断系统是否是深色模式
     * @return
     */
    fun isSystemNightMode(context: Context): Boolean {
        try {
            val uiMode = context.resources.configuration.uiMode
            val nightModeMask = Configuration.UI_MODE_NIGHT_MASK
            val currentNightModeSetting = uiMode.and(nightModeMask)
            return (currentNightModeSetting == Configuration.UI_MODE_NIGHT_YES)
        } catch (_: Exception) {

        }
        return false;
    }

    /**
     * 判断主题是否是深色模式
     * @return
     */
    fun isThemeNightMode(): Boolean {
        return themeMode == ThemeMode.NIGHT
    }

    /**
     * 判断是否是深色模式
     * @return
     */
    fun isNightMode(context: Context): Boolean {
        return isThemeNightMode()
    }

    fun onConfigurationChanged(context: Context, isNightModeActive: Boolean) {
        val themePreferences =
            context.getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        val isNightModeEnabled =
            themePreferences?.getBoolean("mode_night", false) == true
        val isFollowSystemEnabled =
            themePreferences?.getBoolean("follow_system", false) == true
        if (isNightModeEnabled && isFollowSystemEnabled) {
            themeMode = if (isNightModeActive) ThemeMode.NIGHT else ThemeMode.DAY
        } else if (isNightModeEnabled) {
            themeMode = ThemeMode.NIGHT
        }
    }
}