package com.link.riderlink.features.hide.logcat

import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.annotation.RequiresApi
import androidx.fragment.app.Fragment
import androidx.navigation.findNavController
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentDetailedBinding
import com.link.riderlink.utils.ShareFileUtils
import com.link.riderlink.utils.ThemeManager
import com.link.riderlink.utils.popBackStack
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.nio.file.Files
import java.nio.file.Paths
import androidx.core.graphics.toColorInt

class DetailedFragment : Fragment() {
    private var _binding: FragmentDetailedBinding? = null
    private val binding get() = _binding!!
    private val TAG = "DetailedFragment"
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDetailedBinding.inflate(inflater, container, false)
        return binding.root
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        val bundle = arguments
        var path = ""
        bundle?.let {
            val filepath = it.getString("path")
            if (filepath != null) {
                path = filepath
            }
            val title = it.getString("title")
            binding.tvTitle.text = title
            Log.e(TAG, "onViewCreated: $filepath")
            if (filepath != null) {
                getLogcat(filepath)
            }
        }
        binding.txShare.setOnClickListener {
            Log.e(TAG, "onViewCreated: $path")
            ShareFileUtils.shareFile(requireContext(),path)
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        binding.ibBack.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.set_back
            )
        )
        binding.detailRoot.setBackgroundColor(
            ThemeManager.autoChangeStr(
                "#FFFFFF",
                "#000000"
            ).toColorInt()
        )
        binding.tvTitle.setTextColor(
            ThemeManager.autoChangeStr(
                "#202229",
                "#FFFFFF"
            ).toColorInt()
        )
        binding.txDetailed.setTextColor(
            ThemeManager.autoChangeStr(
                "#202229",
                "#FFFFFF"
            ).toColorInt()
        )
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun getLogcat(filepath: String){
        val path = Paths.get(filepath)
        var tx = ""
        Files.lines(path, Charsets.UTF_8).forEach {
            tx += it+"\n"
//            Log.e(TAG, "getLogcat: $it", )
        }
        Log.e(TAG, "getLogcat: $tx")
        binding.txDetailed.text = tx
        binding.txDetailed.movementMethod = ScrollingMovementMethod.getInstance();
    }

    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}