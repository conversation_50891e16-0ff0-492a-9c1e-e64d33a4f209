package com.link.riderlink.features.dvr

import android.graphics.Bitmap

data class DvrItem(
    val mode: String,
    var title: String,
    var cover: Bitmap? = null,
    val url: String,
//    val size: Float,
    var databaseID:Int =0,//数据库id
    val position: Int? = 0,//列表下标
    var progress: Int = 0,//下载进度
    var size: Int = 0,//文件大小
    var schedule: Int = 0,//已经下载
    var downloadStatus: String = "",//下载状态
    var isSelected: Boolean = false,//选中状态
    var isDownloaded: Boolean = false,//是否已下载
    var isDownloading: Boolean = false,//是否在下载列表
    var path: String = "",//本地路径
    val deviceName: String = "",
) {
}
