package com.link.riderlink.features.hide

import com.link.riderlink.utils.ThemeManager
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.navigation.findNavController
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentAppversionBinding
import com.link.riderlink.utils.popBackStack
import com.link.riderservice.utils.Platform
import androidx.core.graphics.toColorInt

class AppVersionFragment : Fragment() {
    private var _binding: FragmentAppversionBinding? = null
    private val binding get() = _binding!!
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAppversionBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        binding.txAppversion.text = Platform.packageName(requireContext())
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }
    fun initTheme(){
        binding.ibBack.setImageResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.set_back))
        binding.appRoot.setBackgroundColor(
            ThemeManager.autoChangeStr("#FFFFFF", "#2A3042").toColorInt())
        binding.tvTitle.setTextColor(ThemeManager.autoChangeStr("#202229", "#FFFFFF").toColorInt())
        binding.txAppversion.setTextColor(
            ThemeManager.autoChangeStr("#202229", "#FFFFFF").toColorInt())
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}