package com.link.riderlink.features.dvr

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.animation.OvershootInterpolator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs
import kotlin.math.hypot
import androidx.core.view.isVisible
import androidx.core.view.size

class LeftWipeRecyclerView : RecyclerView {
    //滚动动画，之所以不用Scroller，是因为Scroller 需要配合ondraw方法使用，而我只需要对child view scroll
    //定义回弹效果的动画
    var swipeAnimator: ValueAnimator = ValueAnimator()

    var minimumSwipeDistance = 0 //最小的滑动单位，超过这个值才被认为是有效的滑动  系统值

    constructor(context: Context?) : this(context, null)
    constructor(context: Context?, attributeSet: AttributeSet?) : this(context, attributeSet, 0)
    constructor(context: Context?, attributeSet: AttributeSet?, defaultStyleAttribute: Int) : super(
        context!!,
        attributeSet,
        defaultStyleAttribute
    ) {
        swipeAnimator.interpolator = OvershootInterpolator(0f)
        layoutManager = LinearLayoutManager(context)

        minimumSwipeDistance = ViewConfiguration.get(context).scaledTouchSlop;
    }

    override fun requestLayout() {
        super.requestLayout()
        if (swipedView != null) {
            swipedView?.scrollTo(0, 0)
            swipedView = null
            isInitialMoveCompleted = false
            shouldHandleTouchEvents = true
        }
    }

    override fun setLayoutManager(layoutManager: LayoutManager?) {
        if (layoutManager is LinearLayoutManager) {
            super.setLayoutManager(layoutManager)
        } else {
            super.setLayoutManager(LinearLayoutManager(context))

        }
    }


    var speed = 1 // 回弹的速度系数

    //是否进行过水平方向移动
    //也就是基于这个判断touch event up 是否要传递给child 触发点击
    var isHorizontallyScrolled = false

    //滑动的View
    var swipedView: View? = null

    //判断是否为第一次收到有效的touch Move
    //避免奇怪的滑动
    var isInitialMoveCompleted: Boolean = false

    //如果进行了y轴方向的滚动，就不处理事件分发，
    // 交由普通RecyclerView的处理
    var shouldHandleTouchEvents: Boolean = true

    var lastTouchCoordinates = FloatArray(2)//记录上一个touch点坐标

    var maxScrollOffset = 0 //记录滑动范围


    var isOtherItemSelected = false //显示隐藏区域时 是否点击了其他位置


    override fun dispatchTouchEvent(motionEvent: MotionEvent?): Boolean {
        //如果在滚动动画中 不接受任何touch事件
        if (isScrolling) return true

        when (motionEvent?.action) {
            MotionEvent.ACTION_DOWN -> {
                //down事件初始化
                isOtherItemSelected = false
                isHorizontallyScrolled = false
                isInitialMoveCompleted = false
                lastTouchCoordinates[0] = motionEvent.x
                lastTouchCoordinates[1] = motionEvent.y

                //判断如果有 滑动区域显示，这个时候touch
                // 漏出的View有效
                //其他的View touch事件拦截，将露出的View 滚回初始状态
                if (swipedView != null) {
                    var touchBounds = Rect()
                    swipedView!!.getHitRect(touchBounds)

                    if (!touchBounds.contains(motionEvent.x.toInt(), motionEvent.y.toInt())) {
                        scrollToIdle(true)
                        isInitialMoveCompleted = false
                        shouldHandleTouchEvents = true
                        //isOtherItemSelected 设置为true 拦截事件传递
                        //{@link #onInterceptTouchEvent}
                        isOtherItemSelected = true

                    }
                    return super.dispatchTouchEvent(motionEvent)

                }


            }

            MotionEvent.ACTION_MOVE -> {
                if (shouldHandleTouchEvents) {
                    //检查是否是滚动的最小单位
                    if (hypot(
                            motionEvent.x.toDouble() - lastTouchCoordinates[0],
                            motionEvent.y.toDouble() - lastTouchCoordinates[1]
                        ) > minimumSwipeDistance
                    ) {
                        //是否是第一次滚动
                        //这样避免在recyclerview 本身滚动的同时左右滑动手指引起奇怪的判断和View状态
                        if (!isInitialMoveCompleted) {
                            //如果满足条件 检查是否是x方向还是y方向滚动
                            if (checkScollHorizon(motionEvent.x, motionEvent.y)) {
                                //第一次滚动 为水平方向滚动
                                //那么就找到并赋值当前的滑动View
                                if (swipedView == null) {
                                    swipedView = findItemByPostion(
                                        lastTouchCoordinates[0].toInt(),
                                        lastTouchCoordinates[1].toInt()
                                    )
                                }
                            }
                            isInitialMoveCompleted = true
                        }

                        if (swipedView != null) {
                            //横向滑动超过一定水平，接下来的点击事件就不要了
                            if (abs(motionEvent.x.toDouble() - lastTouchCoordinates[0]) > minimumSwipeDistance) {
                                isHorizontallyScrolled = true
                            }

                            if (scrollHor(motionEvent)) {
                                return true
                            }
                        } else {
                            //没有滑动目标，那么就不再处理move 事件，
                            //交给recyclerview父类方法处理事件分发
                            lastTouchCoordinates[0] = motionEvent.x
                            lastTouchCoordinates[1] = motionEvent.y
                            shouldHandleTouchEvents = false
                        }
                    } else {
//                        滑动距离小于滑动判断临界
//                        为了效果流程，满足 x方向，并且滑动view不为Null，就横向滑动
                        return scrollHor(motionEvent)
                    }
                }
            }

            MotionEvent.ACTION_UP -> {

//                松手时
                shouldHandleTouchEvents = true
                if (swipedView != null) {

                    //滑动view滚动到指定位置
                    // 默认 OR 显示全部隐藏视图
                    scrollToIdle()

                    //如果水平点击过 事件不向下传递
                    if (isHorizontallyScrolled) {
                        isHorizontallyScrolled = false
                        return true
                    }
                }


            }
        }
        return super.dispatchTouchEvent(motionEvent)
    }

    override fun onInterceptTouchEvent(motionEvent: MotionEvent?): Boolean {

        // isOtherItemSelected 是否拦截事件传递
        return super.onInterceptTouchEvent(motionEvent) || isOtherItemSelected
    }


    //有滑动view后，滑动view跟随手指移动，要注意的是
    // 手指左滑，motionEvent?.x - lastTouchCoordinates[0]<0
    // 而 要显示右边的区域 scrolls 要 >0
    //所以 swipedView!!.scrollX - scrollable
    //即左滑显示右边
    private fun scrollHor(motionEvent: MotionEvent?): Boolean {
        if (motionEvent == null) return false
        if (swipedView != null) {

            if (checkScollHorizon(motionEvent.x, motionEvent.y)) {
                var scrollDelta = (motionEvent.x - lastTouchCoordinates[0]).toInt()
                var targetScrollOffset = swipedView!!.scrollX - scrollDelta
                if (targetScrollOffset >= maxScrollOffset) {
                    targetScrollOffset = maxScrollOffset
                } else if (targetScrollOffset < 0) {
                    targetScrollOffset = 0
                }
                swipedView?.scrollTo(targetScrollOffset, 0)
            }
            lastTouchCoordinates[0] = motionEvent.x
            lastTouchCoordinates[1] = motionEvent.y
        }
        return true
    }

    //检查是否是x方向  判断依据 x方向是y方向的两倍
    fun checkScollHorizon(x: Float, y: Float): Boolean {
        return abs(y - lastTouchCoordinates[1]) * 2 < abs(x - lastTouchCoordinates[0])
    }

    //查找到点击的view
    //遍历 所以的child 的 hit rect
    //找到满足坐标在里面的view
    fun findItemByPostion(x: Int, y: Int): View? {


        var firstVisibleItemPosition =
            (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
        if (firstVisibleItemPosition < 0) {
            return null
        }

        val childCount = size
        val childBounds = Rect()
        for (index in 0 until childCount) {
            val child = getChildAt(index);
            if (child != null && child.isVisible) {
                child.getHitRect(childBounds);
                if (childBounds.contains(x, y)) {

                    //titleholder 需要标示隐藏区域的id为 R.id.scrollable，这样定位隐藏区域的width
                    val scrollableArea =
                        child.findViewById<View>(com.link.riderdvr.R.id.scrollable_dvr)
                    maxScrollOffset = if (scrollableArea.isVisible) {
                        scrollableArea.width
                    } else {
                        0
                    }

                    return child
                }
            }
        }
        return null
    }


    private var isScrolling: Boolean = false

    //滑动的view 松手或者点击别的区域 滚动到合适的位置
    //需要注意 如果是拖回起初位置时，手动释放掉view
    fun scrollToIdle(forceScrollToStart: Boolean = false) {
        if (swipedView != null) {
            var shouldScrollToStart =
                forceScrollToStart || maxScrollOffset > swipedView!!.scrollX * 2
            var targetScrollPosition = if (shouldScrollToStart) 0 else maxScrollOffset
            if (swipedView!!.scrollX == targetScrollPosition) {
                if (targetScrollPosition == 0) {
                    swipedView = null
                }
                return
            }
            swipeAnimator.setIntValues(swipedView!!.scrollX, targetScrollPosition)
            swipeAnimator.addUpdateListener {
                swipedView?.scrollTo(it.animatedValue as Int, 0)
            }
            swipeAnimator.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animator: Animator) {
                    swipedView?.scrollTo(targetScrollPosition, 0)
                    if (targetScrollPosition == 0) {
                        swipedView = null
                    }
                    swipeAnimator.removeAllUpdateListeners()
                    swipeAnimator.removeAllListeners()
                    isScrolling = false
                }
            })
            swipeAnimator.setDuration(abs(maxScrollOffset) / 2 * speed.toLong())
            swipeAnimator.start()
            isScrolling = true
        }
    }

    //释放资源
    override fun onDetachedFromWindow() {
        swipedView = null
        swipeAnimator.cancel()
        swipeAnimator.removeAllUpdateListeners()
        swipeAnimator.removeAllListeners()
        super.onDetachedFromWindow()

    }


}
