package com.link.riderlink.features.hide

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentProductkeyBinding
import com.link.riderlink.utils.ThemeManager
import com.link.riderlink.utils.popBackStack
import com.link.riderservice.api.RiderService
import kotlinx.coroutines.launch

/**
 * ProductKey显示Fragment
 * 功能：显示产品密钥，支持复制到剪贴板
 */
class ProductKeyFragment : Fragment() {

    companion object {
        private const val TAG = "ProductKeyFragment"

        // 主题颜色常量
        private const val DAY_BACKGROUND_COLOR = "#FFFFFF"
        private const val NIGHT_BACKGROUND_COLOR = "#2A3042"
        private const val DAY_TEXT_COLOR = "#202229"
        private const val NIGHT_TEXT_COLOR = "#FFFFFF"

        // 错误信息常量
        private const val ERROR_PRODUCT_KEY_UNAVAILABLE = "产品密钥暂时不可用"
        private const val SUCCESS_COPIED_TO_CLIPBOARD = "已复制到剪贴板"
    }

    private var _binding: FragmentProductkeyBinding? = null
    private val binding get() = _binding!!

    // 产品密钥缓存
    private var productKey: String? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductkeyBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initializeFragment()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置返回按键处理
     */
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    /**
     * 初始化Fragment
     */
    private fun initializeFragment() {
        setupClickListeners()
        loadProductKey()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.apply {
            llBack.setOnClickListener { popBackStack() }

            // 长按复制产品密钥
            txProductkey.setOnLongClickListener {
                copyProductKeyToClipboard()
                true
            }
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }