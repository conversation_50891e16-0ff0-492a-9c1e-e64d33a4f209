package com.link.riderlink.features.hide.logcat

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.link.riderlink.databinding.LogcatItemBinding
import androidx.core.graphics.toColorInt

class LogcatAdapter(
    private val onItemClick: (LogcatItem) -> Unit,
    private val onItemCheckClick: (Boolean, LogcatItem) -> Unit,
    private val onItemLongClick: (View, LogcatItem) -> Unit,
    private val isSelectionModeActive: () -> Boolean,
    private val isNightModeActive: () -> Boolean
) : ListAdapter<LogcatItem, LogcatAdapter.LogcatViewHolder>(DIFF_CALLBACK)  {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LogcatViewHolder {
        return LogcatViewHolder(
            LogcatItemBinding.inflate(
                LayoutInflater.from(parent.context), parent,
                false
            )
        )
    }
    inner class LogcatViewHolder(binding: LogcatItemBinding) :
        RecyclerView.ViewHolder(binding.root) {
        private val filenameTextView: TextView = binding.txLogcatName
        private val itemContainerLayout: ConstraintLayout = binding.clLogcatItem
        private val itemCheckBox: CheckBox = binding.checkboxLogcat
        fun bind(item: LogcatItem) {
            Log.e(TAG, "bind: ")
            filenameTextView.text = item.filename
            itemContainerLayout.setOnClickListener {
                if (!item.isChecked) {
                    onItemClick(item)
                }
            }
            itemCheckBox.setOnClickListener{
                if (it is CheckBox) {
                    val checked: Boolean = it.isChecked
                    onItemCheckClick(checked, item)
                }
            }
            itemContainerLayout.setOnLongClickListener {
                if (!item.isChecked) {
                    onItemLongClick(it, item)
                }
                return@setOnLongClickListener true
            }
            if (isSelectionModeActive()){
                itemCheckBox.visibility = View.VISIBLE
            }else{
                itemCheckBox.visibility = View.GONE
            }
            if (isNightModeActive()){
                filenameTextView.setTextColor("#FFFFFF".toColorInt())
            }else{
                filenameTextView.setTextColor("#000000".toColorInt())
            }
        }
    }

    override fun onBindViewHolder(holder: LogcatViewHolder, position: Int) {
        holder.bind(getItem(position))
    }


    companion object {
        private const val TAG = "LogcatAdapter"
        private val DIFF_CALLBACK: DiffUtil.ItemCallback<LogcatItem> =
            object : DiffUtil.ItemCallback<LogcatItem>() {
                override fun areItemsTheSame(
                    oldItem: LogcatItem,
                    newItem: LogcatItem,
                ): Boolean {
                    return oldItem.filename == newItem.filename && oldItem.isChecked == newItem.isChecked
                }

                override fun areContentsTheSame(
                    oldItem: LogcatItem,
                    newItem: LogcatItem,
                ): Boolean {
                    return true
                }
            }
    }
}