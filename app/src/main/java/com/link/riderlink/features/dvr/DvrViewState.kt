package com.link.riderlink.features.dvr

import com.link.riderdvr.database.model.DownLoadTask

data class DvrViewState(
//    val DvrList: List<DvrItem> = emptyList(),//列表
    val isFullscreen: Boolean = false,//是否全屏
    val isVolumeOn: Boolean = true,//是否开启音量
    val isPlayerPrepared: Boolean = false,//播放器是否加载完毕
    val isPlaying: Boolean = false,//是否在播放
    val isBrightnessControlVisible: Boolean = false,//亮度开关是否显示
    val isPlaybackComplete: Boolean = false,//是否播放完毕
    val isOnlineListRequested: Boolean = true,//是否请求列表完毕
    val downloadStatus: DownloadStatus = DownloadStatus.IDLE,//下载器状态
    val modeStatus: ModeStatus = ModeStatus.Online,//播放模式
    val downloadProgress: Int = 0,//下载进度
    val currentDownloadTask: DownLoadTask = DownLoadTask(),//正在进行的任务
    val speed: String = "0kb/s",//下载速度
    val downloadTasks: List<DownLoadTask> = emptyList(),//下载任务列表
    val downloadedTasks: List<DvrItem> = emptyList(),//下载任务列表
    val onlineList: List<DvrItem> = emptyList(),//在线列表
    val localList: List<DvrItem> = emptyList(),//本地列表
    var selectedPosition : Int = 0,
    val selectedItem: DvrItem ?= null//正在播放的视频
) {
}

sealed class DvrViewAction {
    data class DvrItemClicked(val dvrItem: DvrItem, val position: Int) : DvrViewAction()//点击视频列表
    data class DownloadTaskClick(val dvrItem: DvrItem, val position: Int) : DvrViewAction()//
    data class PlayClicked(val isPlay: Boolean): DvrViewAction()//点击播放、暂停
    data class ModeChangeClicked(val modeStatus: ModeStatus): DvrViewAction()//切换列表、播放模式
    data class DownloadProgress(val progress: Int) : DvrViewAction()//下载进度更新
    data class SpeedChange(val s: String) : DvrViewAction()

    data class DownloadClicked(val item: DvrItem? = null) : DvrViewAction()//点击下载
    object DownloadComplied : DvrViewAction()//下载完成
    object RefreshList : DvrViewAction()//刷新当前列表
    object RequestedOnline : DvrViewAction()//请求在线列表
    object RequestedLocal : DvrViewAction()//请求本地列表
    object RequestedTask : DvrViewAction()//请求本地列表
    object Completed : DvrViewAction()//播放完
    object ChangeScreenClicked : DvrViewAction()//点击屏幕改变
    object ChangeVolumeClicked : DvrViewAction()//点击声音按键
    object BrightnessClicked: DvrViewAction()//点击亮度按键
    object OnPrepared : DvrViewAction()//播放器已准备
}
sealed class ModeStatus {
    object Online : ModeStatus()//在线播放
    object Local: ModeStatus()//本地播放
    object Task : ModeStatus()//下载任务
}
sealed class DownloadStatus {
    object IDLE : DownloadStatus()//空闲
    object Downloading: DownloadStatus()//下载中
}
sealed class DataBaseAction {
    data class InsertTask(val downLoadTask: DownLoadTask) : DataBaseAction()
    data class DeleteTask(val downLoadTask: DownLoadTask) : DataBaseAction()
    data class UpdateTask(val downLoadTask: DownLoadTask) : DataBaseAction()
    data class UpdateState(val downLoadTask: DownLoadTask) : DataBaseAction()
    object LoadTasks : DataBaseAction()
}