package com.link.riderlink.features.dvr

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.media.AudioManager
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import com.link.riderdvr.utils.ThumbnailUtils
import com.link.riderdvr.widget.VerticalSeekBar

class ControllerViewManager(context: Context) {
    private var idleIconImageView: ImageView? = null
    private var mediaSeekBar: SeekBar? = null
    private var brightnessSeekBar: SeekBar? = null
    private var volumeSeekBar: SeekBar? = null
    private var fastForwardButton: ImageView? = null
    private var rewindButton: ImageView? = null
    private var playButton: ImageView? = null
    private var playButtonSecondary: ImageView? = null
    private var brightnessButton: ImageView? = null
    private var volumeButton: ImageView? = null
    private var screenButton: ImageView? = null
    private var downloadButton: ImageView? = null
    private var rewindTextView: TextView? = null
    private var fastForwardTextView: TextView? = null
    private var endTimeTextView: TextView? = null
    private var startTimeTextView: TextView? = null
    private var titleTextView: TextView? = null
    private var audioManager: AudioManager? = null
    private var activity: Activity?=null
    private var mContext: Context?=null
    private var layoutParams: WindowManager.LayoutParams?=null
    private var duration: Long = 0
    private var isDragging = false
    private var isShowing = false
    private var isMediaPrepared = false
    private val handler = MyHandler()
    private var activeSeekBarMode = ""
    private val TAG = "ControllerViewManager"


    fun onPrepared(){
        isDragging = false
        isShowing = true
        handler.sendEmptyMessageDelayed(MSG_HIDE_CONTROLS,
            CONTROLS_VISIBILITY_TIMEOUT_MS.toLong())
        isMediaPrepared = true
        mediaSeekBar?.visibility = View.VISIBLE
        endTimeTextView?.visibility = View.VISIBLE
        startTimeTextView?.visibility = View.VISIBLE
//        brightnessButton?.visibility = View.VISIBLE
        volumeButton?.visibility = View.VISIBLE
        screenButton?.visibility = View.VISIBLE
        downloadButton?.visibility = View.VISIBLE
        titleTextView?.visibility = View.VISIBLE
        idleIconImageView?.visibility = View.GONE
        handler.removeMessages(MSG_UPDATE_PROGRESS)
        handler.sendEmptyMessage(MSG_UPDATE_PROGRESS)
        show()
    }
    fun onCompleted(){
        handler.removeMessages(MSG_UPDATE_PROGRESS)
    }
    fun onRequestChange(){
        isMediaPrepared = false
        mediaSeekBar?.visibility = View.GONE
        endTimeTextView?.visibility = View.GONE
        startTimeTextView?.visibility = View.GONE
//        brightnessButton?.visibility = View.GONE
        volumeButton?.visibility = View.GONE
        screenButton?.visibility = View.GONE
        downloadButton?.visibility = View.GONE
        titleTextView?.visibility = View.GONE
        idleIconImageView?.visibility = View.VISIBLE
        handler.removeMessages(MSG_UPDATE_PROGRESS)
    }

    private fun getGenerateTime(time: Long): String {
        return ThumbnailUtils.instance.getGenerateTime(time)
    }

    private fun setProgress_video() {
        if (isDragging) {
            return
        }
        val position: Long = listener?.getCurrentPosition()!!.toLong()
        val duration: Long = listener?.getDuration()!!.toLong()
        this.duration = duration
        if (getGenerateTime(duration) != endTimeTextView?.text.toString())
            endTimeTextView?.text = getGenerateTime(duration)
        if (mediaSeekBar != null) {
            if (duration > 0) {
                val pos = position.toInt()
                mediaSeekBar!!.max = duration.toInt()
                mediaSeekBar!!.progress = pos
            }
        }
        val string = getGenerateTime(position)
        startTimeTextView?.text = string
    }

    private fun setProgress_brightness() {
        var currentBrightness = layoutParams!!.screenBrightness
        if(currentBrightness == WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE)
        {
            layoutParams!!.screenBrightness =  getScreenBrightness()/ 255f
            currentBrightness = layoutParams!!.screenBrightness
            Log.e("194", currentBrightness.toString())
        }
        brightnessSeekBar!!.progress = (currentBrightness*100).toInt()
        Log.e("197",brightnessSeekBar!!.progress.toString())
    }
    private fun setProgress_volume() {
        val audioManagerSystem = activity?.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val maxVolume = audioManagerSystem.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
        val currentVolume = audioManagerSystem.getStreamVolume(AudioManager.STREAM_MUSIC)
        val volume = currentVolume*100/maxVolume
        volumeSeekBar!!.progress = volume
    }

    private val MSG_HIDE_CONTROLS = 1
    private val MSG_SHOW_CONTROLS = 6
    private val CONTROLS_VISIBILITY_TIMEOUT_MS = 3000
    private val MSG_UPDATE_PROGRESS = 2
    private val MSG_HIDE_PAUSE_ICON = 3
    private val MSG_SEEK_TO_NEW_POSITION = 4
    private val MSG_HIDE_SPECIFIC_CONTROL = 5
    private var newPosition: Long = 1

    fun setvisibilityforcommission(visibility: Int){
        rewindTextView?.visibility = visibility
        fastForwardTextView?.visibility = visibility
        rewindButton?.visibility = visibility
        fastForwardButton?.visibility = visibility
        playButton?.visibility = visibility
        playButtonSecondary?.visibility = visibility
        volumeButton?.visibility = visibility
        downloadButton?.visibility = visibility
        mediaSeekBar?.visibility = visibility
        screenButton?.visibility = visibility
        startTimeTextView?.visibility = visibility
        endTimeTextView?.visibility = visibility
        listener?.showOnIVideo(visibility == View.VISIBLE)
    }

    private inner class MyHandler: Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            val msg1: Message
            super.handleMessage(msg)
            when (msg.what) {
                MSG_HIDE_CONTROLS -> {
                    Log.e("MyMediaController","MSG_HIDE_CONTROLS")
                    setvisibilityforcommission(View.GONE)
                    isShowing = false
                }
                MSG_SHOW_CONTROLS -> {
                    Log.e("MyMediaController","MSG_SHOW_CONTROLS")
                    setvisibilityforcommission(View.VISIBLE)
                    isShowing = true
                }
                MSG_UPDATE_PROGRESS -> {
                    if(isMediaPrepared) {
                        setProgress_video()
                    }else {
                        removeMessages(MSG_UPDATE_PROGRESS)
                    }
                    if (!isDragging) {
                        msg1 = obtainMessage(MSG_UPDATE_PROGRESS)
                        sendMessageDelayed(msg1, 1000)//进度条更新间隔
                    }
                }
                MSG_HIDE_PAUSE_ICON -> {}
//                MSG_SEEK_TO_NEW_POSITION -> if (newPosition >= 0) {
//                    ivideoView?.seekTo(newPosition.toInt())
//                    newPosition = -1
//                }
                MSG_HIDE_SPECIFIC_CONTROL -> {}
            }
        }
    }

    //改变屏幕亮度
    //改变屏幕亮度
    private fun setLightness(lightness:Float){
        //屏幕的亮度,最大是255
        layoutParams!!.screenBrightness =layoutParams!!.screenBrightness+lightness;
        if(layoutParams!!.screenBrightness>1){
            layoutParams!!.screenBrightness= 1F;
        }else if(layoutParams!!.screenBrightness<0.2){
            layoutParams!!.screenBrightness=0.2f;
        }
        activity!!.window.attributes = layoutParams;
    }
    //自定义的接口，可以根据自己的需求修改
    private var listener: ControllerViewListener? = null

    fun setListener(listener: ControllerViewListener) {
        <EMAIL> = listener
    }

    interface ControllerViewListener {
        fun onStopTrackingTouch(progress: Int)
        fun getCurrentPosition(): Int
        fun getDuration() : Int
        fun showOnIVideo(b: Boolean)
    }

    private val mediaSeekBarChangeListener = object :SeekBar.OnSeekBarChangeListener{
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            val string = getGenerateTime(progress.toLong())
            startTimeTextView?.text = string
        }

        override fun onStartTrackingTouch(seekBar: SeekBar) {
            setProgress_video()
            isDragging = true
            listener?.onStopTrackingTouch(seekBar.progress)
            handler.removeMessages(MSG_UPDATE_PROGRESS)
            show()
            handler.removeMessages(MSG_HIDE_CONTROLS)
        }

        override fun onStopTrackingTouch(seekBar: SeekBar) {
            isDragging = false
//            ivideoView!!.seekTo(seekBar.progress)
            listener?.onStopTrackingTouch(seekBar.progress)
            handler.removeMessages(MSG_UPDATE_PROGRESS)
            handler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, 1000)
            show()
        }
    }
    private val brightnessSeekBarChangeListener = object :SeekBar.OnSeekBarChangeListener{
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            val currentBrightness = layoutParams!!.screenBrightness
            if (currentBrightness == WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE) {
                layoutParams!!.screenBrightness = getScreenBrightness() / 255f
            }
            val changedLightness = brightnessSeekBar!!.progress.toFloat()/100-layoutParams!!.screenBrightness
            setLightness(changedLightness)
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {
        }

        override fun onStopTrackingTouch(seekBar: SeekBar) {

        }
    }
    private val volumeSeekBarChangeListener = object :SeekBar.OnSeekBarChangeListener{
        override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
            val audioManagerSystem = activity?.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            val maxVolume = audioManagerSystem.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
//            val mCurrentVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
            val volume = progress * maxVolume/100
            audioManagerSystem.setStreamVolume(AudioManager.STREAM_MUSIC, volume, AudioManager.FLAG_SHOW_UI)
        }

        override fun onStartTrackingTouch(seekBar: SeekBar?) {
        }

        override fun onStopTrackingTouch(seekBar: SeekBar) {

        }
    }

    /**
     * 获取系统默认屏幕亮度值 屏幕亮度值范围（0-255）
     */
    private fun getScreenBrightness(): Int {
        val contentResolver = mContext!!.contentResolver
        val defaultBrightness = 125
        return Settings.System.getInt(contentResolver,
            Settings.System.SCREEN_BRIGHTNESS, defaultBrightness)
    }

    private fun findActivity(context: Context): Activity? {
        return context as? Activity
            ?: if (context is ContextWrapper) {
                findActivity(context.baseContext)
            } else {
                null
            }
    }
    fun setSeekBar_mediat(seekBar: SeekBar?){
        mediaSeekBar = seekBar
    }
    fun setSeekBar_brightness(verticalSeekBar: VerticalSeekBar?){
        brightnessSeekBar = verticalSeekBar
    }
    fun setSeekBar_volume(verticalSeekBar: VerticalSeekBar?){
        volumeSeekBar = verticalSeekBar
    }
    fun setButton_play(imageView: ImageView?){
        playButton = imageView
    }
    fun setButton_play1(imageView: ImageView?){
        playButtonSecondary = imageView
    }
    fun setButton_fastforward(imageView: ImageView?){
        fastForwardButton = imageView
    }
    fun setButton_rewind(imageView: ImageView?){
        rewindButton = imageView
    }
    fun setButton_brightness(imageView: ImageView?){
        brightnessButton = imageView
    }
    fun setText_tvRw(textView: TextView?){
        rewindTextView = textView
    }
    fun setText_tvFf(textView: TextView?){
        fastForwardTextView = textView
    }
    fun setText_tvStart(textView: TextView?){
        startTimeTextView = textView
    }
    fun setText_tvEnd(textView: TextView?){
        endTimeTextView = textView
    }
    fun setidle_icon(imageView: ImageView?){
        idleIconImageView = imageView
    }
    fun settv_title(textView: TextView?){
        titleTextView = textView
    }
    fun setvolume_btn(imageView: ImageView?){
        volumeButton = imageView
    }
    fun setscreen_btn(imageView: ImageView?){
        screenButton = imageView
    }
    fun setdownload_btn(imageView: ImageView?){
        downloadButton = imageView
    }

    fun brightness_volume_show(visibility: Int, percentage: Int, mod: String){
        if (visibility == View.VISIBLE) {
            brightnessButton?.visibility = visibility
            if (mod == "brightnessButton") {
                brightnessButton?.setBackgroundResource(com.link.riderdvr.R.drawable.brightness_selector)
                activeSeekBarMode = "brightnessButton"
                brightnessSeekBar?.let {
                    it.visibility = visibility
                    var progress = it.progress
                    progress += percentage
                    if (progress>100) {
                        progress = 100
                    }
                    if (progress<0) {
                        progress = 0
                    }
                    it.progress = progress
                }
            }else if (mod == "volume"){
                brightnessButton?.setBackgroundResource(com.link.riderdvr.R.drawable.volume_on_selector)
                activeSeekBarMode = "volume"
                volumeSeekBar?.let {
                    it.visibility = visibility
                    var progress = it.progress
                    progress += percentage
                    if (progress>100) {
                        progress = 100
                    }
                    if (progress<0) {
                        progress = 0
                    }
                    it.progress = progress
                }
            }
        }else if (visibility == View.GONE){
            brightnessSeekBar?.visibility = visibility
            volumeSeekBar?.visibility = visibility
            brightnessButton?.visibility = visibility
        }
    }

    fun initToolFanction(){
        mediaSeekBar!!.setOnSeekBarChangeListener(mediaSeekBarChangeListener)
        brightnessSeekBar?.setOnSeekBarChangeListener(brightnessSeekBarChangeListener)
        setProgress_volume()
        volumeSeekBar?.setOnSeekBarChangeListener(volumeSeekBarChangeListener)
        brightnessSeekBar?.visibility = View.GONE
        volumeSeekBar?.visibility = View.GONE
        setvisibilityforcommission(View.GONE)
        setProgress_brightness()
        setProgress_video()
    }
    fun onItemClick(){
        isMediaPrepared = false
        mediaSeekBar?.visibility = View.GONE
        endTimeTextView?.visibility = View.GONE
        startTimeTextView?.visibility = View.GONE
//        brightnessButton?.visibility = View.GONE
        volumeButton?.visibility = View.GONE
        screenButton?.visibility = View.GONE
        downloadButton?.visibility = View.GONE
        titleTextView?.visibility = View.GONE
        idleIconImageView?.visibility = View.VISIBLE
        handler.removeMessages(MSG_UPDATE_PROGRESS)
        hide()
    }

    fun show() {
        handler.removeMessages(MSG_HIDE_CONTROLS)
        handler.removeMessages(MSG_SHOW_CONTROLS)
        handler.sendEmptyMessage(MSG_SHOW_CONTROLS)
        handler.sendEmptyMessageDelayed(MSG_HIDE_CONTROLS, CONTROLS_VISIBILITY_TIMEOUT_MS.toLong())
    }

    fun show(timeout:Int) {
        handler.removeMessages(MSG_HIDE_CONTROLS)
        handler.removeMessages(MSG_SHOW_CONTROLS)
        handler.sendEmptyMessage(MSG_SHOW_CONTROLS)
        handler.sendEmptyMessageDelayed(MSG_HIDE_CONTROLS, timeout.toLong())
    }

    fun hide() {
        handler.removeMessages(MSG_HIDE_CONTROLS)
        handler.removeMessages(MSG_SHOW_CONTROLS)
        handler.sendEmptyMessage(MSG_HIDE_CONTROLS)
    }

    fun media_show(percentage: Float) {
        mediaSeekBar?.let {
            val max = it.max
            var progress = it.progress
            val change = (36000*percentage).toInt()
            progress += change
            if (progress>max) {
                progress = max
            }
            if (progress<0) {
                progress = 0
            }
            setProgress_video()
            isDragging = true
            it.progress = progress
            listener?.onStopTrackingTouch(it.progress)
            handler.removeMessages(MSG_UPDATE_PROGRESS)
            show()
            handler.removeMessages(MSG_HIDE_CONTROLS)
        }
    }

    fun touchOn(){
        if (isDragging) {
            isDragging = false
            mediaSeekBar?.progress?.let { listener?.onStopTrackingTouch(it) }
            handler.removeMessages(MSG_UPDATE_PROGRESS)
            handler.sendEmptyMessageDelayed(MSG_UPDATE_PROGRESS, 1000)
            show()
        }
    }

    init {
        audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        activity = findActivity(context);
        mContext = context
        layoutParams = activity!!.window.attributes;
    }
}