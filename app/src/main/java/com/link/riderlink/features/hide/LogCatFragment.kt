package com.link.riderlink.features.hide

import com.link.riderlink.utils.ThemeManager
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import com.link.riderdvr.connection.LocalManager
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentLogcatBinding
import com.link.riderlink.features.hide.logcat.LogcatAdapter
import com.link.riderlink.features.hide.logcat.LogcatItem
import com.link.riderlink.utils.LogcatHelper
import com.link.riderlink.utils.ShareFileUtils
import com.link.riderlink.utils.navigate
import com.link.riderlink.utils.popBackStack
import java.io.File

class LogCatFragment : Fragment() {
    private val TAG = "LogCatFragment"
    private var _binding: FragmentLogcatBinding? = null
    private val binding get() = _binding!!
    private var title = "logcat"
    private var onCheck = false
    private var switchItem :MutableList<LogcatItem> = ArrayList()
    private val checkClick = {open: Boolean,item: LogcatItem ->
        if (open){
            switchItem.add(item)
        }else{
            switchItem.removeIf { it == item }
        }
        Unit
    }
    private val click = {it: LogcatItem ->
        val bundle = Bundle()
        bundle.putString("path", it.filepath)
        bundle.putString("title", "${title}详情")
        navigate(R.id.action_logcatFragment_to_detailedFragment, bundle)
    }
    private val longclick = {view: View, item: LogcatItem ->
        myPopupMenu(view, item)
    }
    private val oncheck = {
        onCheck
    }
    private val isnight = {
        ThemeManager.isNightMode(requireContext())
    }
    private val adapter: LogcatAdapter by lazy {
        LogcatAdapter (click, checkClick, longclick, oncheck, isnight)
    }


    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
           popBackStack()
        }
    }
    private fun myPopupMenu(v: View, logcatitem: LogcatItem) {
        val popupMenu = PopupMenu(requireActivity(), v);
        popupMenu.menuInflater.inflate(R.menu.logcat_menu, popupMenu.menu);
        popupMenu.setOnMenuItemClickListener { item ->
            Log.e(TAG, "onMenuItemClick: ${item?.itemId}")
            if (item?.itemId == R.id.delete_m) {
                deleteSingleFile(logcatitem.filepath)//删除logcat
                adapter.submitList(getList())
            } else if (item?.itemId == R.id.share_m) {
                ShareFileUtils.shareFile(requireContext(), logcatitem.filepath)
            } else if (item?.itemId == R.id.multiply_m) {
                onCheck = true
                adapter.submitList(getList())
                multiplyBar()
            }
            true
        };
        //显示菜单
        popupMenu.show()
    }

    fun ordinaryBar(){
        binding.tvTitle.visibility = View.VISIBLE
        binding.llBack.visibility = View.VISIBLE
        binding.ibBack.visibility = View.VISIBLE
        binding.txDel.visibility = View.VISIBLE
        binding.cancelTitle.visibility = View.GONE
        binding.shareTitle.visibility = View.GONE
        binding.delTitle.visibility = View.GONE
    }

    fun multiplyBar(){
        binding.tvTitle.visibility = View.GONE
        binding.llBack.visibility = View.GONE
        binding.ibBack.visibility = View.GONE
        binding.txDel.visibility = View.GONE
        binding.cancelTitle.visibility = View.VISIBLE
        binding.shareTitle.visibility = View.VISIBLE
        binding.delTitle.visibility = View.VISIBLE
    }
    fun getList(): List<LogcatItem>{
        val list: MutableList<LogcatItem> = ArrayList()
        val filelist = getFilesAllName()
        if (filelist!=null) {
            for (i in filelist.indices) {
                val logcatItem = LogcatItem(filelist[i].name, filelist[i].absolutePath, onCheck)
                val end = logcatItem.filename.split('.')
                val begin = logcatItem.filename.split('-')
                if (end.last() == "log" && begin.first() == title) {
                    list.add(logcatItem)
                    Log.e(TAG, "" + filelist[i])
                }
            }
        }
        return list
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLogcatBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        onCheck = false
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        binding.llBack.setOnClickListener{
            popBackStack()
        }
        binding.logcatsList.layoutManager = LinearLayoutManager(activity)
        binding.logcatsList.adapter = adapter
        binding.txDel.setOnClickListener {
            deleteAll()
        }
        binding.cancelTitle.setOnClickListener{
            ordinaryBar()
            switchItem.clear()
            onCheck = false
            adapter.submitList(getList())
        }
        binding.shareTitle.setOnClickListener {
            ShareFileUtils.shareFiles(requireContext(), switchItem)
        }
        binding.delTitle.setOnClickListener {
            delectCheck()
            adapter.submitList(getList())
        }
        val bundle = getArguments()
        bundle?.let {
            val title1 = it.getString("param")
            binding.tvTitle.text = title1 + "日志"
            title = title1.toString()
        }
        adapter.submitList(getList())
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }
    fun getFilesAllName(): Array<out File>? {
        val folderPath = LogcatHelper.getInstance(requireContext()).logcatPath
        val file = File(folderPath)
        val files = file.listFiles()
        if (files == null) {
            Log.e("error", "空目录")
            return null
        }
        val s: MutableList<String> = ArrayList()
        for (i in files.indices) {
            s.add(files[i].absolutePath)
            Log.e(TAG,files[i].absolutePath)
        }
        return files
    }

    private fun delectCheck(){
        switchItem.forEach{logcatItem ->
            deleteSingleFile(logcatItem.filepath)
        }
        switchItem.clear()
    }
    private fun deleteAll(){
        val filePath = LogcatHelper.getInstance(requireContext()).logcatPath
        val dirFile = File(filePath)
        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if (!dirFile.exists() || !dirFile.isDirectory) {
            Log.e(LocalManager.TAG,"删除目录失败：" + filePath + "不存在！")
            return
        }
        // 删除文件夹中的所有文件
        val files = dirFile.listFiles()
        for (file in files) {
            // 删除子文件
            if (file.isFile) {
                deleteSingleFile(file.absolutePath)
            }
        }
        val list: MutableList<LogcatItem> = ArrayList()
        adapter.submitList(list)
    }


    /** 删除单个文件
     * @param filePath Name 要删除的文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    fun deleteSingleFile(`filePath$Name`: String): Boolean {
        val file = File(`filePath$Name`)
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除

        val end = file.name.split('.')
        val begin = file.name.split('-')
        return if (file.exists() && file.isFile && end.last() == "log" && begin.first() == title) {
            if (file.delete()) {
                Log.e("--Method--",
                    "Copy_Delete.deleteSingleFile: 删除单个文件" + `filePath$Name` + "成功！")
                true
            } else {
                Log.e(TAG,"删除单个文件" + `filePath$Name` + "失败！")
                false
            }
        } else {
            Log.e(TAG,"删除单个文件失败：" + `filePath$Name` + "不存在！")
            false
        }
    }
    @SuppressLint("NotifyDataSetChanged")
    fun initTheme(){
        binding.ibBack.setImageResource(ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.set_back))
        binding.logcatRoot.setBackgroundColor(Color.parseColor(ThemeManager.autoChangeStr("#FFFFFF","#2A3042")))
        binding.tvTitle.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.txDel.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.delTitle.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.cancelTitle.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        binding.shareTitle.setTextColor(Color.parseColor(ThemeManager.autoChangeStr("#202229","#FFFFFF")))
        adapter.notifyDataSetChanged()
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}