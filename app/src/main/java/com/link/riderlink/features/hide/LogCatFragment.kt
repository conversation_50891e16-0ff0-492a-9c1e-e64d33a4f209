package com.link.riderlink.features.hide

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.link.riderdvr.connection.LocalManager
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentLogcatBinding
import com.link.riderlink.features.hide.logcat.LogcatAdapter
import com.link.riderlink.features.hide.logcat.LogcatItem
import com.link.riderlink.utils.LogcatHelper
import com.link.riderlink.utils.ShareFileUtils
import com.link.riderlink.utils.ThemeManager
import com.link.riderlink.utils.navigate
import com.link.riderlink.utils.popBackStack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import androidx.core.graphics.toColorInt

/**
 * LogCat日志文件管理Fragment
 * 功能：显示、删除、分享LogCat日志文件
 */
class LogCatFragment : Fragment() {

    companion object {
        private const val TAG = "LogCatFragment"
        private const val DEFAULT_TITLE = "logcat"
        private const val LOG_FILE_EXTENSION = "log"
        private const val PARAM_KEY = "param"
        private const val PATH_KEY = "path"
        private const val TITLE_KEY = "title"
        private const val LOG_SUFFIX = "日志"
        private const val DETAIL_SUFFIX = "详情"

        // 主题颜色常量
        private const val DAY_BACKGROUND_COLOR = "#FFFFFF"
        private const val NIGHT_BACKGROUND_COLOR = "#2A3042"
        private const val DAY_TEXT_COLOR = "#202229"
        private const val NIGHT_TEXT_COLOR = "#FFFFFF"
    }

    private var _binding: FragmentLogcatBinding? = null
    private val binding get() = _binding!!

    // 状态变量
    private var logTitle = DEFAULT_TITLE
    private var isSelectionMode = false
    private val selectedItems: MutableList<LogcatItem> = mutableListOf()

    // 适配器回调函数
    private val onItemClick: (LogcatItem) -> Unit = { item ->
        navigateToDetail(item)
    }

    private val onItemCheckClick: (Boolean, LogcatItem) -> Unit = { isChecked, item ->
        handleItemSelection(isChecked, item)
    }

    private val onItemLongClick: (View, LogcatItem) -> Unit = { view, item ->
        showPopupMenu(view, item)
    }

    private val isSelectionModeActive: () -> Boolean = { isSelectionMode }

    private val isNightModeActive: () -> Boolean = {
        ThemeManager.isNightMode(requireContext())
    }

    private val adapter: LogcatAdapter by lazy {
        LogcatAdapter(
            onItemClick,
            onItemCheckClick,
            onItemLongClick,
            isSelectionModeActive,
            isNightModeActive
        )
    }


    override fun onAttach(context: Context) {
        super.onAttach(context)
        setupBackPressHandler()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLogcatBinding.inflate(inflater, container, false)
        return binding.root
    }

    @SuppressLint("SetTextI18n")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initializeFragment()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        cleanup()
    }

    // ==================== 初始化方法 ====================

    /**
     * 设置返回按键处理
     */
    private fun setupBackPressHandler() {
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    /**
     * 初始化Fragment
     */
    private fun initializeFragment() {
        isSelectionMode = false
        setupRecyclerView()
        setupClickListeners()
        parseArguments()
        loadLogFiles()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeChangeListener)
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        binding.logcatsList.apply {
            layoutManager = LinearLayoutManager(activity)
            adapter = <EMAIL>
        }
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        binding.apply {
            llBack.setOnClickListener { popBackStack() }
            txDel.setOnClickListener { deleteAllFiles() }
            cancelTitle.setOnClickListener { exitSelectionMode() }
            shareTitle.setOnClickListener { shareSelectedFiles() }
            delTitle.setOnClickListener { deleteSelectedFiles() }
        }
    }

    /**
     * 解析传入参数
     */
    @SuppressLint("SetTextI18n")
    private fun parseArguments() {
        arguments?.getString(PARAM_KEY)?.let { param ->
            binding.tvTitle.text = param + LOG_SUFFIX
            logTitle = param
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        ThemeManager.unregisterThemeChangeListener(themeChangeListener)
        _binding = null
    }

    // ==================== UI状态管理 ====================

    /**
     * 显示普通模式工具栏
     */
    private fun showNormalToolbar() {
        binding.apply {
            tvTitle.visibility = View.VISIBLE
            llBack.visibility = View.VISIBLE
            ibBack.visibility = View.VISIBLE
            txDel.visibility = View.VISIBLE
            cancelTitle.visibility = View.GONE
            shareTitle.visibility = View.GONE
            delTitle.visibility = View.GONE
        }
    }

    /**
     * 显示多选模式工具栏
     */
    private fun showSelectionToolbar() {
        binding.apply {
            tvTitle.visibility = View.GONE
            llBack.visibility = View.GONE
            ibBack.visibility = View.GONE
            txDel.visibility = View.GONE
            cancelTitle.visibility = View.VISIBLE
            shareTitle.visibility = View.VISIBLE
            delTitle.visibility = View.VISIBLE
        }
    }

    /**
     * 进入选择模式
     */
    private fun enterSelectionMode() {
        isSelectionMode = true
        showSelectionToolbar()
        refreshLogList()
    }

    /**
     * 退出选择模式
     */
    private fun exitSelectionMode() {
        showNormalToolbar()
        selectedItems.clear()
        isSelectionMode = false
        refreshLogList()
    }
    // ==================== 事件处理方法 ====================

    /**
     * 导航到详情页面
     */
    private fun navigateToDetail(item: LogcatItem) {
        val bundle = Bundle().apply {
            putString(PATH_KEY, item.filepath)
            putString(TITLE_KEY, "$logTitle$DETAIL_SUFFIX")
        }
        navigate(R.id.action_logcatFragment_to_detailedFragment, bundle)
    }

    /**
     * 处理项目选择
     */
    private fun handleItemSelection(isChecked: Boolean, item: LogcatItem) {
        if (isChecked) {
            selectedItems.add(item)
        } else {
            selectedItems.removeAll { it == item }
        }
    }

    /**
     * 显示弹出菜单
     */
    private fun showPopupMenu(view: View, item: LogcatItem) {
        val popupMenu = PopupMenu(requireActivity(), view)
        popupMenu.menuInflater.inflate(R.menu.logcat_menu, popupMenu.menu)
        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem?.itemId) {
                R.id.delete_m -> {
                    deleteSingleFileAsync(item.filepath)
                    true
                }
                R.id.share_m -> {
                    ShareFileUtils.shareFile(requireContext(), item.filepath)
                    true
                }
                R.id.multiply_m -> {
                    enterSelectionMode()
                    true
                }
                else -> false
            }
        }
        popupMenu.show()
    }

    /**
     * 分享选中的文件
     */
    private fun shareSelectedFiles() {
        if (selectedItems.isNotEmpty()) {
            ShareFileUtils.shareFiles(requireContext(), selectedItems)
        }
    }

    /**
     * 删除选中的文件
     */
    private fun deleteSelectedFiles() {
        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                selectedItems.forEach { item ->
                    deleteSingleFile(item.filepath)
                }
            }
            withContext(Dispatchers.Main) {
                selectedItems.clear()
                refreshLogList()
            }
        }
    }

    /**
     * 删除所有文件
     */
    private fun deleteAllFiles() {
        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                deleteAllLogFiles()
            }
            withContext(Dispatchers.Main) {
                adapter.submitList(emptyList())
            }
        }
    }
    // ==================== 数据操作方法 ====================

    /**
     * 加载日志文件列表
     */
    private fun loadLogFiles() {
        lifecycleScope.launch {
            val logItems = withContext(Dispatchers.IO) {
                getLogFileList()
            }
            adapter.submitList(logItems)
        }
    }

    /**
     * 刷新日志列表
     */
    private fun refreshLogList() {
        loadLogFiles()
    }

    /**
     * 获取日志文件列表
     */
    private suspend fun getLogFileList(): List<LogcatItem> = withContext(Dispatchers.IO) {
        val logItems = mutableListOf<LogcatItem>()
        try {
            val files = getAllLogFiles()
            files?.forEach { file ->
                if (isValidLogFile(file)) {
                    val logItem = LogcatItem(
                        filename = file.name,
                        filepath = file.absolutePath,
                        isChecked = isSelectionMode
                    )
                    logItems.add(logItem)
                    Log.d(TAG, "Found log file: ${file.absolutePath}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading log files", e)
        }
        logItems
    }

    /**
     * 获取所有文件
     */
    private fun getAllLogFiles(): Array<File>? {
        return try {
            val folderPath = LogcatHelper.getInstance(requireContext()).logcatPath
            val directory = File(folderPath)

            if (!directory.exists() || !directory.isDirectory) {
                Log.w(TAG, "Log directory does not exist: $folderPath")
                return null
            }

            directory.listFiles()
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing log directory", e)
            null
        }
    }

    /**
     * 验证是否为有效的日志文件
     */
    private fun isValidLogFile(file: File): Boolean {
        if (!file.isFile) return false

        val fileName = file.name
        val fileExtension = fileName.substringAfterLast('.', "")
        val filePrefix = fileName.substringBefore('-', "")

        return fileExtension == LOG_FILE_EXTENSION && filePrefix == logTitle
    }

    /**
     * 异步删除单个文件
     */
    private fun deleteSingleFileAsync(filePath: String) {
        lifecycleScope.launch {
            val success = withContext(Dispatchers.IO) {
                deleteSingleFile(filePath)
            }
            if (success) {
                refreshLogList()
            }
        }
    }

    /**
     * 删除单个文件
     * @param filePath 要删除的文件路径
     * @return 删除成功返回true，否则返回false
     */
    private fun deleteSingleFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)

            if (!file.exists() || !file.isFile) {
                Log.w(TAG, "File does not exist or is not a file: $filePath")
                return false
            }

            if (!isValidLogFile(file)) {
                Log.w(TAG, "Invalid log file: $filePath")
                return false
            }

            val deleted = file.delete()
            if (deleted) {
                Log.d(TAG, "Successfully deleted file: $filePath")
            } else {
                Log.e(TAG, "Failed to delete file: $filePath")
            }
            deleted
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting file: $filePath", e)
            false
        }
    }

    /**
     * 删除所有日志文件
     */
    private fun deleteAllLogFiles() {
        try {
            val files = getAllLogFiles()
            files?.forEach { file ->
                if (file.isFile && isValidLogFile(file)) {
                    deleteSingleFile(file.absolutePath)
                }
            }
            Log.d(TAG, "All log files deleted")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting all files", e)
        }
    }
    // ==================== 主题管理 ====================

    /**
     * 初始化主题
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun initTheme() {
        binding.apply {
            // 设置返回按钮图标
            ibBack.setImageResource(
                ThemeManager.getCurrentThemeRes(requireContext(), R.drawable.set_back)
            )

            // 设置背景颜色
            logcatRoot.setBackgroundColor(
                ThemeManager.autoChangeStr(DAY_BACKGROUND_COLOR, NIGHT_BACKGROUND_COLOR)
                    .toColorInt()
            )

            // 设置文本颜色
            val textColor =
                ThemeManager.autoChangeStr(DAY_TEXT_COLOR, NIGHT_TEXT_COLOR).toColorInt()

            tvTitle.setTextColor(textColor)
            txDel.setTextColor(textColor)
            delTitle.setTextColor(textColor)
            cancelTitle.setTextColor(textColor)
            shareTitle.setTextColor(textColor)
        }

        // 通知适配器数据变化以更新主题
        adapter.notifyDataSetChanged()
    }

    /**
     * 主题变化监听器
     */
    private val themeChangeListener = object : ThemeManager.OnThemeChangeListener() {
        override fun onThemeChanged() {
            initTheme()
        }
    }
}