package com.link.riderlink.features.dvr

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.link.riderlink.databinding.DvrViewBinding
import androidx.core.graphics.toColorInt


/**
 * <AUTHOR>
 * @date 2024/1/16
 */

internal class DvrButtonView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defaultStyleAttribute: Int = 0
) : ConstraintLayout(context, attrs, defaultStyleAttribute) {
    private val binding: DvrViewBinding =
        DvrViewBinding.inflate(LayoutInflater.from(context), this, true)

    private var isNightMode = false

    override fun setSelected(isSelected: Boolean) {
        super.setSelected(isSelected)
        initTheme(isSelected)
    }

    fun setText(buttonTitle: String){
        binding.titleBtn.text = buttonTitle
    }

    fun setTextColor(buttonTextColor: String){
        binding.titleBtn.setTextColor(buttonTextColor.toColorInt())
    }

    fun initTheme(isSelected: Boolean){
        if (isSelected) {
            binding.titleBtn.setTextColor("#5C7BD7".toColorInt())
            binding.dvrSelectedImg.visibility = VISIBLE
        } else {
            binding.titleBtn.setTextColor("#8C909E".toColorInt())
            binding.dvrSelectedImg.visibility = GONE
        }
        setBackgroundColor(selectThemeString("#FFFFFF", "#2A3042").toColorInt())
    }

    fun selectThemeInt(dayThemeColor: Int, nightThemeColor: Int): Int{
        if(isNightMode){
            return nightThemeColor
        }else{
            return dayThemeColor
        }
    }
    fun selectThemeString(dayThemeColor: String, nightThemeColor: String): String{
        if(isNightMode){
            return nightThemeColor
        }else{
            return dayThemeColor
        }
    }

    fun onThemeChange(isNightMode: Boolean){
        this.isNightMode = isNightMode
        initTheme(isSelected)
    }
}




