package com.link.riderlink.features.hide

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.link.riderlink.R
import com.link.riderlink.RiderLink
import com.link.riderlink.databinding.FragmentRiderlinkversionBinding
import com.link.riderlink.utils.ThemeManager
import com.link.riderlink.utils.popBackStack
import com.link.riderservice.api.RiderServiceCallback

class RiderLinkVersionFragment : Fragment() {
    private var _binding: FragmentRiderlinkversionBinding? = null
    private val binding get() = _binding!!
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentRiderlinkversionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        RiderLink.instance.addConnectCallback(mCallback)
        val bundle = arguments
        bundle?.let {
            val tx = it.getString("param")
            binding.txRiderlinkversion.text = tx
        }
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        binding.ibBack.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.set_back
            )
        )
        binding.riderlinkRoot.setBackgroundColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#FFFFFF",
                    "#2A3042"
                )
            )
        )
        binding.tvTitle.setTextColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#202229",
                    "#FFFFFF"
                )
            )
        )
        binding.txRiderlinkversion.setTextColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#202229",
                    "#FFFFFF"
                )
            )
        )
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }

    private val mCallback = object : RiderServiceCallback(){
        override fun naviversionResponse(version: String) {
            super.naviversionResponse(version)
            binding.txRiderlinkversion.text = version
        }
    }
}