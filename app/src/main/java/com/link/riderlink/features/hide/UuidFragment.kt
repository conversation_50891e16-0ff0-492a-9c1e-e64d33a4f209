package com.link.riderlink.features.hide

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.addCallback
import androidx.fragment.app.Fragment
import androidx.navigation.findNavController
import com.link.riderlink.R
import com.link.riderlink.databinding.FragmentAppversionBinding
import com.link.riderlink.databinding.FragmentUuidBinding
import com.link.riderlink.utils.ThemeManager
import com.link.riderlink.utils.popBackStack
import com.link.riderservice.api.RiderService

class UuidFragment : Fragment() {
    private var _binding: FragmentUuidBinding? = null
    private val binding get() = _binding!!
    override fun onAttach(context: Context) {
        super.onAttach(context)
        requireActivity().onBackPressedDispatcher.addCallback(this) {
            popBackStack()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUuidBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        binding.llBack.setOnClickListener {
            popBackStack()
        }
        binding.txUuid.text = RiderService.instance.getUuid()
        initTheme()
        ThemeManager.registerThemeChangeListener(themeCallback)
    }

    fun initTheme() {
        binding.ibBack.setImageResource(
            ThemeManager.getCurrentThemeRes(
                requireContext(),
                R.drawable.set_back
            )
        )
        binding.uuidRoot.setBackgroundColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#FFFFFF",
                    "#2A3042"
                )
            )
        )
        binding.tvTitle.setTextColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#202229",
                    "#FFFFFF"
                )
            )
        )
        binding.txUuid.setTextColor(
            Color.parseColor(
                ThemeManager.autoChangeStr(
                    "#202229",
                    "#FFFFFF"
                )
            )
        )
    }
    val themeCallback = object: ThemeManager.OnThemeChangeListener(){
        override fun onThemeChanged() {
            initTheme()
        }
    }
    override fun onDestroyView() {
        super.onDestroyView()
        ThemeManager.unregisterThemeChangeListener(themeCallback)
        _binding = null
    }
}