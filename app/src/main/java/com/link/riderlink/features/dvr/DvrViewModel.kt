package com.link.riderlink.features.dvr

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hjq.toast.ToastUtils
import com.link.riderdvr.connection.LocalManager
import com.link.riderdvr.database.model.DownLoadTask
import com.link.riderdvr.database.model.DownLoadTaskProgress
import com.link.riderdvr.database.model.DownLoadTaskStatus
import com.link.riderdvr.repository.DownLoadTaskRepository
import com.link.riderdvr.utils.mainScope
import com.link.riderlink.R
import com.link.riderlink.utils.setState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import me.jessyan.autosize.AutoSizeConfig
import org.json.JSONException
import org.json.JSONObject
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.net.MalformedURLException
import java.net.URL
import java.net.URLConnection


class DvrViewModel : ViewModel() {
    @SuppressLint("StaticFieldLeak")
    private val repository: DownLoadTaskRepository = DownLoadTaskRepository()

    private val _viewStates = MutableStateFlow(DvrViewState())

    val viewStates = _viewStates.asStateFlow()

    private var currentDvrItem: DvrItem? = null
    private var currentDownloadTask: DownLoadTask? = null
    private var dvrEventListener: DvrListener? = null
    val context: Context = AutoSizeConfig.getInstance().application.applicationContext
//    internal DvrViewModelListener(){
//
//    }

    private var localFileManager = LocalManager(object : LocalManager.DownLoadListener {

        override fun downLoadError(message: String) {
            pauseAll()
            mainScope.launch(Dispatchers.Main) {
                Toast.makeText(
                    context,
                    context.getString(R.string.dvr_download_error) + ":$message",
                    Toast.LENGTH_LONG
                ).show()
            }
        }

        override fun downloadIdle() {
            _viewStates.setState { copy(downloadStatus = DownloadStatus.IDLE) }//下载任务暂停，下载器闲置
            Log.e(
                TAG + 63,
                "_viewStates.setState { copy(downloadStatus = DownloadStatus.IDLE) }//下载任务结束，下载器闲置"
            )
            find_task_in_list()
        }

        override fun onInsert(task: DownLoadTask, title: String) {
            dispatch_database(DataBaseAction.InsertTask(task))
            Log.e(TAG, "onInsert: 62: $task")
            val dvrItemFromTask = DvrItem(
                mode = "task",
                title = task.title,
                url = task.url,
                databaseID = task.id,
                progress = task.progress,
                size = task.size,
                schedule = task.schedule,
                downloadStatus = task.state,
                path = task.path,
                isDownloading = true,
                isDownloaded = false
            )
            val dvrItems: MutableList<DvrItem> = viewStates.value.downloadedTasks.toMutableList()
            val downloadTasks: MutableList<DownLoadTask> = viewStates.value.downloadTasks.toMutableList()
            dvrItems.add(dvrItemFromTask)
            downloadTasks.add(task)
            _viewStates.setState { copy(downloadedTasks = dvrItems) }
            _viewStates.setState { copy(downloadTasks = downloadTasks) }

            refresh_downloading_items(dvrItemFromTask, false, true)
            if (_viewStates.value.downloadStatus == DownloadStatus.IDLE) {//如果下载模块闲置，启动下载
                find_task_in_list()
            }
            requestLocaldvr()
            mainScope.launch(Dispatchers.Main) {
                Toast.makeText(
                    context,
                    context.getString(R.string.dvr_download_start, title),
                    Toast.LENGTH_SHORT
                ).show()
            }
        }

        override fun startDownLoad(nowTask: DownLoadTask, progress: Int) {
            Log.e(TAG, "startDownLoad: progress=${nowTask.progress}")
            dispatch_database(DataBaseAction.UpdateState(nowTask))
            dispatch_database(DataBaseAction.UpdateTask(nowTask))
        }

        override fun updateTask(nowTask: DownLoadTask) {
            dispatch_database(DataBaseAction.UpdateTask(nowTask))
        }
    })

    fun setListener(listener: DvrListener) {
        dvrEventListener = listener
    }

    fun dispatch(viewAction: DvrViewAction) {
        when (viewAction) {
            is DvrViewAction.DvrItemClicked -> Preparing(viewAction.dvrItem, viewAction.position)
            is DvrViewAction.DownloadTaskClick -> downloadtaskclick(
                viewAction.dvrItem,
                viewAction.position
            )

            is DvrViewAction.DownloadClicked -> addTask(viewAction.item)
            is DvrViewAction.DownloadProgress -> downloadProgress(viewAction.progress)
            is DvrViewAction.DownloadComplied -> downloadComplied()
            is DvrViewAction.PlayClicked -> play(viewAction.isPlay)
            is DvrViewAction.RequestedOnline -> requestList()
            is DvrViewAction.RequestedLocal -> dispatch_database(DataBaseAction.LoadTasks)
            is DvrViewAction.RequestedTask -> isrequesttask()
            is DvrViewAction.RefreshList -> isrefreshList()
            is DvrViewAction.Completed -> completed()
            is DvrViewAction.ChangeScreenClicked -> changeScreen()
            is DvrViewAction.ChangeVolumeClicked -> changeVolume()
            is DvrViewAction.BrightnessClicked -> brightnessOn()
            is DvrViewAction.OnPrepared -> onPrepared()
            is DvrViewAction.ModeChangeClicked -> ismodeChange(viewAction.modeStatus)
            is DvrViewAction.SpeedChange -> speedchange(viewAction.s)
        }
    }

    private fun speedchange(s: String) {
        _viewStates.setState { copy(speed = s) }
    }

    private fun downloadtaskclick(dvrItem: DvrItem, position: Int) {//点击下载任务后触发的事件
        Log.e(TAG + 110, dvrItem.downloadStatus)
        _viewStates.setState { copy(selectedPosition = position) }
        if (dvrItem.downloadStatus == "completed") {
            return
        } else if (dvrItem.downloadStatus == "prepare") {
            dvrItem.downloadStatus = "pause"
        } else if (dvrItem.downloadStatus == "downloading") {
            dvrItem.downloadStatus = "pause"
            localFileManager.pauseTask()//下载暂停
        } else if (dvrItem.downloadStatus == "pause") {
            dvrItem.downloadStatus = "prepare"
        }
        val onlineDvrItems = viewStates.value.onlineList
        val currentDvrItems = viewStates.value.downloadedTasks
        val currentDownloadTasks = viewStates.value.downloadTasks
        var position1 = 0
        var databaseid = 0
        onlineDvrItems.indices.forEach {
            if (onlineDvrItems[it].url == dvrItem.url) {
                onlineDvrItems[it].downloadStatus = dvrItem.downloadStatus
                position1 = it
            }
        }
        currentDvrItems.indices.forEach {
            if (currentDvrItems[it].url == dvrItem.url) {
                databaseid = currentDvrItems[it].databaseID
                currentDvrItems[it].downloadStatus = dvrItem.downloadStatus
            }
        }
        _viewStates.setState {
            copy(onlineList = onlineDvrItems)
        }
        _viewStates.setState {
            copy(downloadedTasks = currentDvrItems)
        }
        _viewStates.setState {
            copy(downloadTasks = currentDownloadTasks)
        }
//        StateChange(dvrItem.databaseID, dvrItem.state)//更新list
        dispatch_database(
            DataBaseAction.UpdateState(
                DownLoadTask(
                    id = databaseid,
                    state = dvrItem.downloadStatus
                )
            )
        )
        Log.e(TAG, "dispatch_database: ${onlineDvrItems[position1]}")

        dvrEventListener?.progressChanged(position1)//更新点击选项的状态
        Log.e(
            TAG,
            "dispatch_database:IDLE: ${viewStates.value.downloadStatus == DownloadStatus.IDLE}，扫描下载任务",
        )
        if (viewStates.value.downloadStatus is DownloadStatus.IDLE) {
            find_task_in_list()//点击下载后，判断
        }
    }

    /*
    数据库操作
    */
    @SuppressLint("SuspiciousIndentation")
    fun dispatch_database(viewAction: DataBaseAction) {
        when (viewAction) {
            is DataBaseAction.LoadTasks -> {//获取并刷新任务列表
                viewModelScope.launch {
                    val localFiles: MutableList<DownLoadTask> = ArrayList()
                    val taskFiles: MutableList<DvrItem> = ArrayList()
                    loadTasks().let {
                        _viewStates.setState { copy(downloadedTasks = taskFiles) }
                        it.forEach { d ->
//                            if(d.state == "downloading"&&mapViewState.value.downloadStatus == DownloadStatus.IDLE){
//                                d.state = "prepare"
//                                viewModelScope.launch {
//                                    updataState(DownLoadTaskStatus(d.id,d.state))
//                                }
//                            }
                            val Item = DvrItem(
                                mode = "task",
                                title = d.title,
                                url = d.url,
                                databaseID = d.id,
                                progress = d.progress,
                                size = d.size,
                                schedule = d.schedule,
                                downloadStatus = d.state,
                                path = d.path,
                                isDownloading = true
                            )
                            localFiles.add(d)
                            taskFiles.add(Item)
                        }
                        _viewStates.setState {
                            copy(downloadTasks = localFiles)
                        }
                        _viewStates.setState {
                            copy(downloadedTasks = taskFiles)
                        }
                        if (_viewStates.value.downloadStatus == DownloadStatus.IDLE) {//如果下载模块闲置，启动下载
                            find_task_in_list()
                        }
                    }
                    requestLocaldvr()
                    mainScope.launch(Dispatchers.IO) {
                        getOnlineList()
                    }
                }
            }

            is DataBaseAction.InsertTask -> {//插入任务
                viewModelScope.launch {
                    insertTask(viewAction.downLoadTask)
                }
            }

            is DataBaseAction.DeleteTask -> {//删除任务
                mainScope.launch(Dispatchers.IO) {
                    Log.e(TAG + 99, "DeleteTask:" + findNew())
                    Log.e(TAG + 99, "DeleteTask:" + viewAction.downLoadTask)
                }
                viewModelScope.launch {
                    deleteTask(viewAction.downLoadTask)
                }
                val dvrItems = viewStates.value.downloadedTasks.toMutableList()
                val downloadTasks = viewStates.value.downloadTasks.toMutableList()
                dvrItems.removeIf {
                    it.databaseID == viewAction.downLoadTask.id
                }
                downloadTasks.removeIf {
                    it.id == viewAction.downLoadTask.id
                }
                _viewStates.setState {
                    copy(downloadedTasks = dvrItems)
                }
                _viewStates.setState {
                    copy(downloadTasks = downloadTasks)
                }
            }

            is DataBaseAction.UpdateTask -> {
                viewModelScope.launch {
                    updateTask(viewAction.downLoadTask)//更新数据库成员
                }
                val localFiles = viewStates.value.downloadedTasks
                val taskFiles = viewStates.value.downloadTasks
                val currentOnlineDvrItems = viewStates.value.onlineList
                -1
                var position1 = -1
                for (i in localFiles.indices) {
                    if (localFiles[i].databaseID == viewAction.downLoadTask.id) {
                        localFiles[i].downloadStatus = viewAction.downLoadTask.state
                        localFiles[i].progress = viewAction.downLoadTask.progress
                        localFiles[i].schedule = viewAction.downLoadTask.schedule
                    }
                }
                for (i in taskFiles.indices) {
                    if (taskFiles[i].id == viewAction.downLoadTask.id) {
                        taskFiles[i].state = viewAction.downLoadTask.state
                        taskFiles[i].schedule = viewAction.downLoadTask.schedule
                        taskFiles[i].progress = viewAction.downLoadTask.progress
                    }
                }
                for (i in currentOnlineDvrItems.indices) {
                    if (currentOnlineDvrItems[i].url == viewAction.downLoadTask.url) {
                        currentOnlineDvrItems[i].downloadStatus = viewAction.downLoadTask.state
                        currentOnlineDvrItems[i].progress = viewAction.downLoadTask.progress
                        currentOnlineDvrItems[i].schedule = viewAction.downLoadTask.schedule
                        if (viewAction.downLoadTask.state == "completed") {
                            currentOnlineDvrItems[i].isDownloading = false
                            currentOnlineDvrItems[i].isDownloaded = true
                            currentOnlineDvrItems[i].path = viewAction.downLoadTask.path
                        } else {
                            currentOnlineDvrItems[i].isDownloading = true
                            currentOnlineDvrItems[i].isDownloaded = false
                        }
                        position1 = i
                    }
                }
                _viewStates.setState { copy(downloadedTasks = localFiles) }
                _viewStates.setState { copy(downloadTasks = taskFiles) }
                _viewStates.setState { copy(onlineList = currentOnlineDvrItems) }
//                dispatch(DvrViewAction.DownloadProgress(viewAction.downLoadTask.progress))
                dvrEventListener?.progressChanged(position1)
                dispatch(DvrViewAction.SpeedChange(localFileManager.requestDownLoadSpeed()))
                if (viewAction.downLoadTask.state == "completed") {//下载完成
                    dispatch(DvrViewAction.DownloadComplied)
                    dispatch_database(DataBaseAction.DeleteTask(DownLoadTask(id = viewAction.downLoadTask.id)))//删除任务
                    Log.e(TAG, "255:${viewAction.downLoadTask.id}")
                    requestLocaldvr()//更新本地列表
                }
            }

            is DataBaseAction.UpdateState -> {
                viewModelScope.launch {
                    updataState(
                        DownLoadTaskStatus(
                            viewAction.downLoadTask.id,
                            viewAction.downLoadTask.state
                        )
                    )
                }

                val localFiles = viewStates.value.downloadedTasks
                val taskFiles = viewStates.value.downloadTasks
                val currentOnlineDvrItems = viewStates.value.onlineList
                for (i in localFiles.indices) {
                    if (localFiles[i].databaseID == viewAction.downLoadTask.id)
                        localFiles[i].downloadStatus = viewAction.downLoadTask.state
                    localFiles[i].isDownloading = true
                }
                for (i in taskFiles.indices) {
                    if (taskFiles[i].id == viewAction.downLoadTask.id)
                        taskFiles[i].state = viewAction.downLoadTask.state
                }
                for (i in currentOnlineDvrItems.indices) {
                    if (currentOnlineDvrItems[i].url == viewAction.downLoadTask.url)
                        currentOnlineDvrItems[i].downloadStatus = viewAction.downLoadTask.state
                }
                _viewStates.setState { copy(downloadedTasks = localFiles) }
                _viewStates.setState { copy(downloadTasks = taskFiles) }
                _viewStates.setState { copy(onlineList = currentOnlineDvrItems) }
            }
        }
    }

    private suspend fun insertTask(downLoadTask: DownLoadTask) =
        repository.insertTask(downLoadTask)

    private suspend fun deleteTask(downLoadTask: DownLoadTask) =
        repository.deleteTask(downLoadTask)

    private suspend fun updateTask(downLoadTask: DownLoadTask) =
        repository.updateTask(downLoadTask)


    private suspend fun updataState(state: DownLoadTaskStatus) =
        repository.updateTaskState(state)


    private suspend fun updataProgress(progress: DownLoadTaskProgress) =
        repository.updateTaskProgress(progress)

    private suspend fun loadTasks(): List<DownLoadTask> = repository.loadTasks()

    fun findTasksByTitle(title: String): DownLoadTask = repository.findTasksByTitle(title)

    fun findNew(): Int = repository.findNew()

    private fun downloadComplied() {
        _viewStates.setState { copy(downloadStatus = DownloadStatus.IDLE) }//下载完成，下载器闲置
        Log.e(
            TAG + 275,
            "_viewStates.setState { copy(downloadStatus = DownloadStatus.IDLE) }//下载完成，下载器闲置"
        )
    }

    private fun downloadProgress(progress: Int) {//更新Progress和list
        val localFiles = viewStates.value.downloadedTasks
        var item: DvrItem? = null
        for (i in localFiles.indices) {
            if (i == get_downloading_posion_task()) {
                localFiles[get_downloading_posion_task()].progress = progress
                item = localFiles[get_downloading_posion_task()]
            }
        }
        val taskFiles = viewStates.value.downloadTasks
        for (i in taskFiles.indices) {
            if (item != null) {
                if (taskFiles[i].id == item.databaseID) {
                    taskFiles[i].progress = progress
                    _viewStates.setState { copy(currentDownloadTask = taskFiles[i]) }
                }
            }
        }
        val currentOnlineDvrItems = viewStates.value.onlineList
        var position = 0
        for (i in currentOnlineDvrItems.indices) {
            if (currentOnlineDvrItems[i].url == item?.url) {
                currentOnlineDvrItems[i].progress = progress
                position = i
            }
        }
        _viewStates.setState { copy(downloadedTasks = localFiles) }
        _viewStates.setState { copy(downloadTasks = taskFiles) }
        _viewStates.setState { copy(onlineList = currentOnlineDvrItems) }
        dvrEventListener?.progressChanged(position)
        if (viewStates.value.downloadStatus == DownloadStatus.Downloading) {
//            localFileManager.updateNotification(progress)
            _viewStates.setState { copy(downloadProgress = progress) }
            dispatch(DvrViewAction.SpeedChange(localFileManager.requestDownLoadSpeed()))
        }
//        Log.e(TAG+267, ""+mapViewState.value.downloadProgress)
//        Log.e(TAG+268, ""+get_downloading_posion())
    }

    private fun onPrepared() {
        _viewStates.setState { copy(isPlayerPrepared = true) }
    }

    fun gettitle(): String? {
        return currentDvrItem?.title
    }

    fun getbitmap(): Bitmap? {
        return currentDvrItem?.cover
    }

    fun get_database_id(url: String): Int {
        viewStates.value.downloadTasks.forEach {
            if (it.url == url) {
                return it.id
            }
        }
        return 0
    }

    fun get_filepath(url: String): String {
        viewStates.value.downloadedTasks.forEach {
            if (it.url == url) {
                return it.path
            }
        }
        return ""
    }

    private fun Preparing(dvr: DvrItem, position: Int) {
        currentDvrItem = dvr
        _viewStates.setState { copy(isPlayerPrepared = false) }
        _viewStates.setState { copy(isPlaybackComplete = false) }
        _viewStates.setState { copy(selectedItem = dvr) }
        refresh_select_items(dvr, position)
    }

    private fun requestList() {
        _viewStates.setState { copy(isOnlineListRequested = false) }
//        _viewStates.setState { copy(isPlayerPrepared = false) }
        dispatch_database(DataBaseAction.LoadTasks)
    }

    @SuppressLint("SuspiciousIndentation")
    private fun requestLocaldvr() {
//        val list_empty: MutableList<DvrItem> = ArrayList()
        val onlineDvrItems: MutableList<DvrItem> = ArrayList()
        val filelist = localFileManager.getFilesAllName()
        filelist?.forEach {
            Log.e(TAG, "requestLocaldvr:${it.name}")
        }
        val tasklist = viewStates.value.downloadedTasks
        if (filelist != null)
            for (i in filelist.indices) {
                val dvrItem = DvrItem(
                    "local",
                    filelist[i].name,
                    null,
                    url = filelist[i].absolutePath,
                    path = filelist[i].absolutePath
                )
                var istask = false
                Log.e(TAG, "requestLocaldvr: istask = false")
                if (filelist[i].name == viewStates.value.selectedItem?.title) {
                    dvrItem.isSelected = true
                }
                for (item in tasklist) {
                    if (item.title == dvrItem.title) {
                        Log.e(TAG, "requestLocaldvr: tasklist: ${dvrItem.title}")
                        istask = true
                    }
                }
                dvrItem.isDownloading = istask
                dvrItem.isDownloaded = !istask
                onlineDvrItems.add(dvrItem)
                Log.e(TAG + 411, "DvrAdapter99: $dvrItem")
            }
        _viewStates.setState { copy(localList = onlineDvrItems) }
//        if(_viewStates.value.modeStatus == ModeStatus.Local)
//            _viewStates.setState { copy(DvrList = list) }
    }

    private fun isrequesttask() {
        dispatch_database(DataBaseAction.LoadTasks)//请求任务列表，更新列表
        _viewStates.setState { copy(modeStatus = ModeStatus.Task) }
//        _viewStates.setState { copy(downloadedTasks = list) }
//        if(_viewStates.value.modeStatus == ModeStatus.Task)
//            _viewStates.setState { copy(DvrList = list) }
    }

    private fun isrefreshList() {
        when (_viewStates.value.modeStatus) {
            is ModeStatus.Online -> {
                dispatch(DvrViewAction.RequestedOnline)
            }

            is ModeStatus.Local -> {
                dispatch(DvrViewAction.RequestedLocal)
            }

            is ModeStatus.Task -> {
                dispatch(DvrViewAction.RequestedTask)
            }
        }
    }

    private fun ismodeChange(modeStatus: ModeStatus) {
        when (modeStatus) {
            is ModeStatus.Online -> {
                _viewStates.setState {
                    copy(
//                        DvrList = onlineList,
                        modeStatus = modeStatus
                    )
                }
            }

            is ModeStatus.Local -> {
                dispatch(DvrViewAction.RequestedLocal)
                _viewStates.setState {
                    copy(
//                        DvrList = localList,
                        modeStatus = modeStatus
                    )
                }
            }

            is ModeStatus.Task -> {
                dispatch(DvrViewAction.RequestedTask)
                _viewStates.setState {
                    copy(
//                DvrList = downloadedTasks,
                        modeStatus = modeStatus
                    )
                }
                Log.e(TAG, "request")
                _viewStates.value.downloadedTasks.forEach {
                    Log.e(TAG, "" + it)
                }
            }
        }
    }

    private fun changeScreen() {//屏幕状态更改
        if (_viewStates.value.isPlayerPrepared)
            _viewStates.setState { copy(isFullscreen = !isFullscreen) }
    }

    private fun addTask(item: DvrItem? = null) {
        if (item != null) {
            mainScope.launch(Dispatchers.IO) {
                localFileManager.set_download_task(findNew() + 1, item.url, item.title)
//                dispatch_database(DataBaseAction.LoadTasks)//添加任务完成，更新任务列表
            }
            refresh_downloading_items(item, false, true)
        } else if (_viewStates.value.isPlayerPrepared && _viewStates.value.modeStatus == ModeStatus.Online) {//已选择视频&&在线播放
            mainScope.launch(Dispatchers.IO) {
                localFileManager.set_download_task(findNew() + 1, currentDvrItem!!.url, currentDvrItem!!.title)
//                dispatch_database(DataBaseAction.LoadTasks)//添加任务完成，更新任务列表
            }
            refresh_downloading_items(currentDvrItem!!, false, true)
        }

    }

    fun get_downloading_posion_task(): Int {
        viewStates.value.downloadedTasks.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].downloadStatus == "downloading") {
//                    Log.e(TAG+489,""+i)
                    return i
                }
            }
        }
        return 0
    }

    fun get_downloading_posion_online(): Int {
        val itemTask = viewStates.value.downloadedTasks[get_downloading_posion_task()]
        viewStates.value.onlineList.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].url == itemTask.url) {
//                    Log.e(TAG+511,""+i)
                    return i
                }
            }
        }
        return 0
    }

    fun refresh_select_items(dvr: DvrItem, position: Int) {
        val localFiles: MutableList<DvrItem> = ArrayList()
        val taskFiles: MutableList<DvrItem> = ArrayList()
        var previousOnlineSelectionIndex = 0
        var previousLocalSelectionIndex = 0
        viewStates.value.onlineList.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].title == dvr.title) {
                    onlineDvrItems[i].isSelected = true
                    localFiles.add(onlineDvrItems[i])
                    Log.e(TAG, dvr.title)
                } else {
                    if (onlineDvrItems[i].isSelected) {
                        previousOnlineSelectionIndex = i
                    }
                    onlineDvrItems[i].isSelected = false
                    localFiles.add(onlineDvrItems[i])
                }
            }
        }
        viewStates.value.localList.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].title == dvr.title) {
                    onlineDvrItems[i].isSelected = true
                    taskFiles.add(onlineDvrItems[i])
                    Log.e(TAG, dvr.title)
                } else {
                    if (onlineDvrItems[i].isSelected) {
                        previousLocalSelectionIndex = i
                    }
                    onlineDvrItems[i].isSelected = false
                    taskFiles.add(onlineDvrItems[i])
                }
            }
        }
        _viewStates.setState { copy(onlineList = localFiles) }
        _viewStates.setState { copy(localList = taskFiles) }
        dvrEventListener?.selectedChanged(previousOnlineSelectionIndex)
        dvrEventListener?.selectedChanged(previousLocalSelectionIndex)
        dvrEventListener?.selectedChanged(position)
    }

    fun refresh_downloading_items(dvr: DvrItem, isdownload: Boolean, isdownloading: Boolean) {
        val localFiles: MutableList<DvrItem> = ArrayList()
        var position = 0
        viewStates.value.onlineList.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].title == dvr.title) {
                    Log.e(TAG, "refresh_downloading_items: ${dvr.title}")
                    if (viewStates.value.downloadStatus is DownloadStatus.IDLE) {
                        onlineDvrItems[i].downloadStatus = "downloading"
                    } else if (viewStates.value.downloadStatus is DownloadStatus.Downloading) {
                        onlineDvrItems[i].downloadStatus = "prepare"
                    }
                    onlineDvrItems[i].isDownloading = isdownloading
                    onlineDvrItems[i].isDownloaded = isdownload
                    Log.e(TAG, dvr.title)
                    position = i
                }
                localFiles.add(onlineDvrItems[i])
            }
        }
        localFiles.forEach {
            Log.e(TAG, "refresh_downloading_items553: $it")
        }
        _viewStates.setState { copy(onlineList = localFiles) }
        dvrEventListener?.selectedChanged(position)
    }

    fun get_downloading_item(): DvrItem? {
        viewStates.value.downloadedTasks.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                if (onlineDvrItems[i].downloadStatus == "downloading") {
//                    Log.e(TAG+389,""+i)
                    return onlineDvrItems[i]
                }
            }
        }
        return null
    }

    fun find_task_in_list() {
        Log.e(TAG, "find_task_in_list: 625: 扫描下载任务")
        viewStates.value.downloadTasks.let { onlineDvrItems ->
            for (i in onlineDvrItems.indices) {
                Log.e(TAG, "find_task_in_list: 获取任务： $i")
                if (onlineDvrItems[i].state == "prepare") {
                    _viewStates.setState { copy(downloadStatus = DownloadStatus.Downloading) }
                    Log.e(TAG, "find_task_in_list: 627: 启动下载器")
                    val downLoadTask = onlineDvrItems[i]
                    Log.e(TAG, "628:find_task_in_list: ${downLoadTask}")
                    downLoadTask.state = "downloading"
                    viewModelScope.launch(Dispatchers.IO) {
                        localFileManager.start_download(downLoadTask)
                    }
                    return//只执行一次下载任务
                }
            }
            _viewStates.setState { copy(currentDownloadTask = DownLoadTask(id = 0)) }//沒有在进行的任务
        }
    }

    fun pauseAll() {
        viewStates.value.downloadedTasks.let {
            for (i in it.indices) {
                if (it[i].downloadStatus == "downloading" || it[i].downloadStatus == "prepare") {
                    downloadtaskclick(it[i], i)
                }
            }
        }
    }

    private fun play(isPlaying: Boolean) {
        Log.e(TAG, "_viewStates.setState { copy(isPlaying = $isPlaying) }")
        _viewStates.setState { copy(isPlaying = isPlaying) }
        _viewStates.setState { copy(isPlaybackComplete = false) }
    }

    private fun brightnessOn() {
        _viewStates.setState { copy(isBrightnessControlVisible = !isBrightnessControlVisible) }
    }

    private fun changeVolume() {
        _viewStates.setState { copy(isVolumeOn = !isVolumeOn) }
    }

    private fun completed() {
        _viewStates.setState { copy(isPlaybackComplete = true) }
    }

    private fun getURLContent(): String {
        val result = StringBuilder()
        try {
            val urlObject = URL(REQUESTURL)
            val uc: URLConnection = urlObject.openConnection()
            //这里的编码格式根据传输的数据的格式来定，不然会出现乱码
            val `in` = BufferedReader(InputStreamReader(uc.getInputStream(), "UTF-8"))
            var inputLine: String?
            while (`in`.readLine().also { inputLine = it } != null) {
                result.append("$inputLine")
            }
            `in`.close()
        } catch (e: MalformedURLException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return result.toString()
    }

    private fun getOnlineList() {
        val onlineDvrItems: MutableList<DvrItem> = ArrayList()
//        val list_empty: MutableList<DvrItem> = ArrayList()
//        _viewStates.setState { copy(DvrList = list_empty) }
        val content = getURLContent()
//        val content = getURLContent()
        Log.e(TAG + 456, "" + content)
        //拿到结果
        if (content != "") {
            //拿到结果
            val json = JSONObject(content)
            try {
                val array = json.getJSONArray("files")
                for (i in 0 until array.length()) {
                    val obj = array.getJSONObject(i)
                    val name = obj.getString("name")
                    val path = obj.getString("path")
                    val localFiles = name.split('.')
                    localFiles.last()
                    if (localFiles[0] == "" || (localFiles.last() != "mp4" && localFiles.last() != "MP4")) {
                        continue
                    }
                    val dvrItem = DvrItem("online", name, null, VIDEOURL + path)
                    viewStates.value.localList.let { localFiles ->
                        for (localFileIndex in localFiles.indices) {
                            if (localFiles[localFileIndex].title == dvrItem.title) {
                                dvrItem.isDownloaded = true
                                dvrItem.isDownloading = false
                                dvrItem.path = localFiles[localFileIndex].path
                                Log.e(TAG, "DvrAdapter99:localList: $localFiles")
                            }
                        }
                    }
                    viewStates.value.downloadedTasks.let { taskFiles ->
                        for (taskFileIndex in taskFiles.indices) {
                            if (taskFiles[taskFileIndex].url == dvrItem.url) {
                                dvrItem.databaseID = taskFiles[taskFileIndex].databaseID
                                dvrItem.isDownloaded = taskFiles[taskFileIndex].isDownloaded
                                dvrItem.isDownloading = taskFiles[taskFileIndex].isDownloading
                                dvrItem.progress = taskFiles[taskFileIndex].progress
                                dvrItem.downloadStatus = taskFiles[taskFileIndex].downloadStatus
                                dvrItem.path = taskFiles[taskFileIndex].path
                                Log.e(TAG, "DvrAdapter99:downloadedTasks: $taskFiles")
                            }
                        }
                    }
                    Log.e(TAG, "DvrAdapter99: getOnlineList: $dvrItem")
                    onlineDvrItems.add(dvrItem)
                }
            } catch (e: JSONException) {
                ToastUtils.show("获取视频失败：${e.message}")
            }
        }
        onlineDvrItems.forEach {
            Log.e(TAG, "getOnlineList: $it")
        }
        _viewStates.setState { copy(onlineList = onlineDvrItems) }
        _viewStates.setState { copy(isOnlineListRequested = true) }
    }

    fun setLocalPath(path: String) {
        localFileManager.setPath(path)
    }

    fun deleteSingleFile(path: String, item: DvrItem) {
        localFileManager.deleteSingleFile(path)
        refresh_downloading_items(item, false, false)
    }

    interface DvrListener {
        fun progressChanged(position: Int)
        fun selectedChanged(position: Int)
    }

    companion object {
        const val TAG = "DvrViewModel"
        const val REQUESTURL = "http://192.168.0.1/usr/local/bin/findVideoFiles"
        const val VIDEOURL = "http://192.168.0.1"
    }
}